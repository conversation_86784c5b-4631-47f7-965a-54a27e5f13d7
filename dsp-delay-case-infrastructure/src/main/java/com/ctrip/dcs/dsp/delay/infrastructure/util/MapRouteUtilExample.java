package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * MapRouteUtil使用示例
 * 展示如何使用地图路径查询工具类
 * 
 * <AUTHOR>
 */
@Component
public class MapRouteUtilExample {

    private static final Logger logger = LoggerFactory.getLogger(MapRouteUtilExample.class);

    @Autowired
    private DelayDspMapRouteUtil mapRouteUtil;

    /**
     * 单个路径查询示例
     */
    public void singleRouteQueryExample() {
        try {
            // 创建起点
            MapRouteUtil.MapPoint fromPoint = new MapRouteUtil.MapPoint();
            fromPoint.setLongitude(116.397128); // 北京天安门经度
            fromPoint.setLatitude(39.916527);   // 北京天安门纬度
            fromPoint.setCoordsys("gcj02");     // 坐标系类型
            fromPoint.setCityId(1);             // 北京城市ID

            // 创建终点
            MapRouteUtil.MapPoint toPoint = new MapRouteUtil.MapPoint();
            toPoint.setLongitude(116.326419);   // 北京西站经度
            toPoint.setLatitude(39.896423);     // 北京西站纬度
            toPoint.setCoordsys("gcj02");       // 坐标系类型
            toPoint.setCityId(1);               // 北京城市ID

            // 创建路径查询请求
            MapRouteUtil.RouteRequest request = new MapRouteUtil.RouteRequest();
            request.setFromPoint(fromPoint);
            request.setToPoint(toPoint);
            request.setOrderId("ORDER_12345");
            request.setDepartureTime(new Date(System.currentTimeMillis() + 3600000)); // 1小时后出发

            // 执行单个路径查询
            MapRouteUtil.RouteResponse response = mapRouteUtil.queryRoute(request);

            // 处理查询结果
            if (response.isSuccess()) {
                logger.info("Single route query success: distance={} km, duration={} min", 
                    response.getDistance(), response.getDuration());
            } else {
                logger.error("Single route query failed: {}", response.getErrorMessage());
            }

        } catch (Exception e) {
            logger.error("Single route query example error", e);
        }
    }

    /**
     * 批量路径查询示例
     */
    public void batchRouteQueryExample() {
        try {
            List<MapRouteUtil.RouteRequest> requests = new ArrayList<>();

            // 创建第一个查询请求
            MapRouteUtil.RouteRequest request1 = createRouteRequest(
                116.397128, 39.916527,  // 天安门
                116.326419, 39.896423,  // 北京西站
                1, "ORDER_001"
            );
            requests.add(request1);

            // 创建第二个查询请求
            MapRouteUtil.RouteRequest request2 = createRouteRequest(
                121.473701, 31.230416,  // 上海人民广场
                121.336319, 31.198362,  // 上海虹桥机场
                2, "ORDER_002"
            );
            requests.add(request2);

            // 创建第三个查询请求
            MapRouteUtil.RouteRequest request3 = createRouteRequest(
                113.280637, 23.125178,  // 广州天河城
                113.299999, 23.392093,  // 广州白云机场
                3, "ORDER_003"
            );
            requests.add(request3);

            // 执行批量路径查询
            List<MapRouteUtil.RouteResponse> responses = mapRouteUtil.queryRoutesBatch(requests);

            // 处理查询结果
            for (int i = 0; i < responses.size(); i++) {
                MapRouteUtil.RouteResponse response = responses.get(i);
                if (response.isSuccess()) {
                    logger.info("Batch route query {} success: distance={} km, duration={} min", 
                        i + 1, response.getDistance(), response.getDuration());
                } else {
                    logger.error("Batch route query {} failed: {}", i + 1, response.getErrorMessage());
                }
            }

        } catch (Exception e) {
            logger.error("Batch route query example error", e);
        }
    }

    /**
     * 创建路径查询请求的辅助方法
     */
    private MapRouteUtil.RouteRequest createRouteRequest(double fromLng, double fromLat, 
                                                        double toLng, double toLat, 
                                                        Integer cityId, String orderId) {
        // 创建起点
        MapRouteUtil.MapPoint fromPoint = new MapRouteUtil.MapPoint();
        fromPoint.setLongitude(fromLng);
        fromPoint.setLatitude(fromLat);
        fromPoint.setCoordsys("gcj02");
        fromPoint.setCityId(cityId);

        // 创建终点
        MapRouteUtil.MapPoint toPoint = new MapRouteUtil.MapPoint();
        toPoint.setLongitude(toLng);
        toPoint.setLatitude(toLat);
        toPoint.setCoordsys("gcj02");
        toPoint.setCityId(cityId);

        // 创建请求
        MapRouteUtil.RouteRequest request = new MapRouteUtil.RouteRequest();
        request.setFromPoint(fromPoint);
        request.setToPoint(toPoint);
        request.setOrderId(orderId);
        request.setDepartureTime(new Date(System.currentTimeMillis() + 3600000)); // 1小时后出发

        return request;
    }

    /**
     * 测试不同场景的示例
     */
    public void testDifferentScenarios() {
        logger.info("=== Testing different scenarios ===");

        // 场景1：城市在白名单中，时段在白名单中 - 应该调用queryEstimateRoute
        testScenario("Scenario 1: City and time in whitelist", 1, new Date(System.currentTimeMillis() + 3600000));

        // 场景2：城市不在白名单中 - 应该兜底走批量方法
        testScenario("Scenario 2: City not in whitelist", 999, new Date(System.currentTimeMillis() + 3600000));

        // 场景3：时段不在白名单中 - 应该兜底走批量方法
        testScenario("Scenario 3: Time not in whitelist", 1, new Date(System.currentTimeMillis() - 3600000));

        // 场景4：没有出发时间 - 应该兜底走批量方法
        testScenario("Scenario 4: No departure time", 1, null);
    }

    private void testScenario(String scenarioName, Integer cityId, Date departureTime) {
        try {
            logger.info("Testing: {}", scenarioName);

            MapRouteUtil.RouteRequest request = createRouteRequest(
                116.397128, 39.916527,  // 天安门
                116.326419, 39.896423,  // 北京西站
                cityId, "TEST_ORDER_" + System.currentTimeMillis()
            );
            request.setDepartureTime(departureTime);

            MapRouteUtil.RouteResponse response = mapRouteUtil.queryRoute(request);

            if (response.isSuccess()) {
                logger.info("{} - Success: distance={} km, duration={} min", 
                    scenarioName, response.getDistance(), response.getDuration());
            } else {
                logger.warn("{} - Failed: {}", scenarioName, response.getErrorMessage());
            }

        } catch (Exception e) {
            logger.error("{} - Error", scenarioName, e);
        }
    }

    /**
     * 性能测试示例
     */
    public void performanceTestExample() {
        try {
            logger.info("=== Performance Test ===");
            
            int testCount = 10;
            List<MapRouteUtil.RouteRequest> requests = new ArrayList<>();
            
            // 创建测试请求
            for (int i = 0; i < testCount; i++) {
                MapRouteUtil.RouteRequest request = createRouteRequest(
                    116.397128 + i * 0.001, 39.916527 + i * 0.001,  // 起点稍微偏移
                    116.326419 + i * 0.001, 39.896423 + i * 0.001,  // 终点稍微偏移
                    1, "PERF_TEST_" + i
                );
                requests.add(request);
            }
            
            // 测试批量查询性能
            long startTime = System.currentTimeMillis();
            List<MapRouteUtil.RouteResponse> responses = mapRouteUtil.queryRoutesBatch(requests);
            long endTime = System.currentTimeMillis();
            
            int successCount = 0;
            for (MapRouteUtil.RouteResponse response : responses) {
                if (response.isSuccess()) {
                    successCount++;
                }
            }
            
            logger.info("Performance test completed: {} requests, {} successful, {} ms total, {} ms avg", 
                testCount, successCount, (endTime - startTime), (endTime - startTime) / testCount);
                
        } catch (Exception e) {
            logger.error("Performance test error", e);
        }
    }
}
