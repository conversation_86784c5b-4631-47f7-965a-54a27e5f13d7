package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.DcsMapDomainServiceProxy;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.dcs.basic.map.application.service.interfaces.BaseGpsDTO;
import com.ctrip.dcs.location.application.service.interfaces.dto.ExtendInfoDTO;
import com.ctrip.dcs.location.application.service.message.QueryPredictRouteRequestType;
import com.ctrip.dcs.location.application.service.message.QueryPredictRouteResponseType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 地图路径查询工具类
 * 独立可复用的地图调用模块，支持批量和单个路径查询
 *
 * <AUTHOR>
 */
@Component
public abstract class MapRouteUtil {

    private static final Logger logger = LoggerFactory.getLogger(MapRouteUtil.class);

    @Autowired
    private DcsMapDomainServiceProxy dcsMapDomainServiceProxy;

    @Autowired
    private MapRouteConfig mapRouteConfig;

    @Autowired
    private GeoGateway geoGateway;

    @Autowired
    private ExecutorService delayGaoDeFutureThreadPool;

    /**
     * 地图点位信息
     */
    public static class MapPoint {
        private Double longitude;
        private Double latitude;
        private String coordsys;
        private Integer cityId;
        private String orderId;
        private Date departureTime;

        public MapPoint() {}

        public MapPoint(Double longitude, Double latitude, String coordsys, Integer cityId, String orderId, Date departureTime) {
            this.longitude = longitude;
            this.latitude = latitude;
            this.coordsys = coordsys;
            this.cityId = cityId;
            this.orderId = orderId;
            this.departureTime = departureTime;
        }

        // Getters and Setters
        public Double getLongitude() { return longitude; }
        public void setLongitude(Double longitude) { this.longitude = longitude; }
        public Double getLatitude() { return latitude; }
        public void setLatitude(Double latitude) { this.latitude = latitude; }
        public String getCoordsys() { return coordsys; }
        public void setCoordsys(String coordsys) { this.coordsys = coordsys; }
        public Integer getCityId() { return cityId; }
        public void setCityId(Integer cityId) { this.cityId = cityId; }
        public String getOrderId() { return orderId; }
        public void setOrderId(String orderId) { this.orderId = orderId; }
        public Date getDepartureTime() { return departureTime; }
        public void setDepartureTime(Date departureTime) { this.departureTime = departureTime; }
    }

    /**
     * 路径查询请求对象
     */
    public static class RouteRequest {
        private MapPoint fromPoint;
        private MapPoint toPoint;
        private String orderId;
        private Date departureTime;

        public RouteRequest() {}

        public RouteRequest(MapPoint fromPoint, MapPoint toPoint, String orderId, Date departureTime) {
            this.fromPoint = fromPoint;
            this.toPoint = toPoint;
            this.orderId = orderId;
            this.departureTime = departureTime;
        }

        // Getters and Setters
        public MapPoint getFromPoint() { return fromPoint; }
        public void setFromPoint(MapPoint fromPoint) { this.fromPoint = fromPoint; }
        public MapPoint getToPoint() { return toPoint; }
        public void setToPoint(MapPoint toPoint) { this.toPoint = toPoint; }
        public String getOrderId() { return orderId; }
        public void setOrderId(String orderId) { this.orderId = orderId; }
        public Date getDepartureTime() { return departureTime; }
        public void setDepartureTime(Date departureTime) { this.departureTime = departureTime; }
    }

    /**
     * 路径查询响应对象
     */
    public static class RouteResponse {
        private Double fromLongitude;
        private Double fromLatitude;
        private String fromCoordsys;
        private Double toLongitude;
        private Double toLatitude;
        private String toCoordsys;
        private Double duration; // 时长（分钟）
        private Double distance; // 距离（公里）
        private boolean success;
        private String errorMessage;

        public RouteResponse() {}

        public RouteResponse(Double fromLongitude, Double fromLatitude, String fromCoordsys,
                           Double toLongitude, Double toLatitude, String toCoordsys,
                           Double duration, Double distance) {
            this.fromLongitude = fromLongitude;
            this.fromLatitude = fromLatitude;
            this.fromCoordsys = fromCoordsys;
            this.toLongitude = toLongitude;
            this.toLatitude = toLatitude;
            this.toCoordsys = toCoordsys;
            this.duration = duration;
            this.distance = distance;
            this.success = true;
        }

        // Getters and Setters
        public Double getFromLongitude() { return fromLongitude; }
        public void setFromLongitude(Double fromLongitude) { this.fromLongitude = fromLongitude; }
        public Double getFromLatitude() { return fromLatitude; }
        public void setFromLatitude(Double fromLatitude) { this.fromLatitude = fromLatitude; }
        public String getFromCoordsys() { return fromCoordsys; }
        public void setFromCoordsys(String fromCoordsys) { this.fromCoordsys = fromCoordsys; }
        public Double getToLongitude() { return toLongitude; }
        public void setToLongitude(Double toLongitude) { this.toLongitude = toLongitude; }
        public Double getToLatitude() { return toLatitude; }
        public void setToLatitude(Double toLatitude) { this.toLatitude = toLatitude; }
        public String getToCoordsys() { return toCoordsys; }
        public void setToCoordsys(String toCoordsys) { this.toCoordsys = toCoordsys; }
        public Double getDuration() { return duration; }
        public void setDuration(Double duration) { this.duration = duration; }
        public Double getDistance() { return distance; }
        public void setDistance(Double distance) { this.distance = distance; }
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }

    /**
     * 批量查询路径
     * 对请求进行分类：可以调用未来预估的走queryEstimateRoute，无法调用的走兜底方法
     *
     * @param requests 路径查询请求列表
     * @return 路径查询响应列表
     */
    public List<RouteResponse> queryRoutesBatch(List<RouteRequest> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            return new ArrayList<>();
        }

        // 分类请求：可以调用未来预估的 vs 需要兜底的
        List<RouteRequest> futureRequests = new ArrayList<>();
        List<RouteRequest> fallbackRequests = new ArrayList<>();

        for (RouteRequest request : requests) {
            if (canUseQueryEstimateRoute(request)) {
                futureRequests.add(request);
            } else {
                fallbackRequests.add(request);
            }
        }

        logger.info("MapRouteUtil.queryRoutesBatch - classified requests: future={}, fallback={}",
            futureRequests.size(), fallbackRequests.size());

        List<RouteResponse> responses = new ArrayList<>();
        List<CompletableFuture<RouteResponse>> futures = new ArrayList<>();

        // 处理可以调用未来预估的请求
        for (RouteRequest request : futureRequests) {
            CompletableFuture<RouteResponse> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return callQueryEstimateRoute(request);
                } catch (Exception e) {
                    logger.error("MapRouteUtil.queryRoutesBatch future route error for request: {}", request.getOrderId(), e);
                    RouteResponse errorResponse = new RouteResponse();
                    errorResponse.setSuccess(false);
                    errorResponse.setErrorMessage(e.getMessage());
                    return errorResponse;
                }
            }, delayGaoDeFutureThreadPool);
            futures.add(future);
        }

        // 处理需要兜底的请求
        if (!fallbackRequests.isEmpty()) {
            CompletableFuture<List<RouteResponse>> fallbackFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return fallbackBatchMethod(fallbackRequests);
                } catch (Exception e) {
                    logger.error("MapRouteUtil.queryRoutesBatch fallback error", e);
                    List<RouteResponse> errorResponses = new ArrayList<>();
                    for (RouteRequest request : fallbackRequests) {
                        RouteResponse errorResponse = new RouteResponse();
                        errorResponse.setSuccess(false);
                        errorResponse.setErrorMessage("Fallback method failed: " + e.getMessage());
                        errorResponses.add(errorResponse);
                    }
                    return errorResponses;
                }
            }, delayGaoDeFutureThreadPool);

            try {
                List<RouteResponse> fallbackResponses = fallbackFuture.get();
                responses.addAll(fallbackResponses);
            } catch (Exception e) {
                logger.error("MapRouteUtil.queryRoutesBatch - fallback future.get() error", e);
            }
        }

        // 等待所有未来预估任务完成并收集结果
        for (CompletableFuture<RouteResponse> future : futures) {
            try {
                RouteResponse response = future.get();
                if (response != null) {
                    responses.add(response);
                }
            } catch (Exception e) {
                logger.error("MapRouteUtil.queryRoutesBatch - future.get() error", e);
            }
        }

        return responses;
    }

    /**
     * 单个查询路径
     * 
     * @param request 路径查询请求
     * @return 路径查询响应
     */
    public RouteResponse queryRoute(RouteRequest request) {
        if (request == null) {
            RouteResponse errorResponse = new RouteResponse();
            errorResponse.setSuccess(false);
            errorResponse.setErrorMessage("Request cannot be null");
            return errorResponse;
        }

        try {
            return queryRouteInternal(request);
        } catch (Exception e) {
            logger.error("MapRouteUtil.queryRoute error for request: {}", request.getOrderId(), e);
            RouteResponse errorResponse = new RouteResponse();
            errorResponse.setSuccess(false);
            errorResponse.setErrorMessage(e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 内部路径查询方法
     * 包含城市白名单和时段白名单的判断逻辑
     */
    private RouteResponse queryRouteInternal(RouteRequest request) {
        // 参数校验
        if (!validateRequest(request)) {
            RouteResponse errorResponse = new RouteResponse();
            errorResponse.setSuccess(false);
            errorResponse.setErrorMessage("Invalid request parameters");
            return errorResponse;
        }

        // 判断是否可以调用未来预估方法
        if (canUseQueryEstimateRoute(request)) {
            logger.info("MapRouteUtil.queryRouteInternal", "using queryEstimateRoute for orderId: {}", request.getOrderId());
            return callQueryEstimateRoute(request);
        } else {
            logger.info("MapRouteUtil.queryRouteInternal", "using fallback method for orderId: {}", request.getOrderId());
            return fallbackSingleMethod(request);
        }
    }

    /**
     * 判断是否可以调用queryEstimateRoute方法
     *
     * @param request 路径查询请求
     * @return true-可以调用，false-不可以调用
     */
    private boolean canUseQueryEstimateRoute(RouteRequest request) {
        if (request == null) {
            return false;
        }

        Integer cityId = request.getFromPoint() != null ? request.getFromPoint().getCityId() : null;
        Date departureTime = request.getDepartureTime();

        // 使用配置类进行综合判断
        return mapRouteConfig.shouldUseGaodeFuture(cityId, departureTime);
    }

    /**
     * 参数校验
     */
    private boolean validateRequest(RouteRequest request) {
        if (request == null) {
            return false;
        }

        MapPoint fromPoint = request.getFromPoint();
        MapPoint toPoint = request.getToPoint();

        if (fromPoint == null || toPoint == null) {
            return false;
        }

        if (fromPoint.getLongitude() == null || fromPoint.getLatitude() == null ||
            toPoint.getLongitude() == null || toPoint.getLatitude() == null) {
            return false;
        }

        if (fromPoint.getCityId() == null || toPoint.getCityId() == null) {
            return false;
        }

        return true;
    }

    /**
     * 单个请求的兜底方法 - 抽象方法，由各项目自己实现
     * 当无法调用queryEstimateRoute时的兜底处理
     *
     * @param request 路径查询请求
     * @return 路径查询响应
     */
    protected abstract RouteResponse fallbackSingleMethod(RouteRequest request);

    /**
     * 批量请求的兜底方法 - 抽象方法，由各项目自己实现
     * 当无法调用queryEstimateRoute时的批量兜底处理
     *
     * @param requests 路径查询请求列表
     * @return 路径查询响应列表
     */
    protected abstract List<RouteResponse> fallbackBatchMethod(List<RouteRequest> requests);

    /**
     * 调用queryEstimateRoute方法
     */
    private RouteResponse callQueryEstimateRoute(RouteRequest request) {
        try {
            QueryPredictRouteRequestType routeRequest = buildQueryPredictRouteRequest(request);
            QueryPredictRouteResponseType response = dcsMapDomainServiceProxy.queryEstimateRoute(routeRequest);

            if (response == null) {
                logger.warn("MapRouteUtil.callQueryEstimateRoute", "queryPredictRouteResponseType is null");
                return createErrorResponse("Response is null");
            }

            if (response.getResponseResult() == null) {
                logger.warn("MapRouteUtil.callQueryEstimateRoute", "responseResult is null");
                return createErrorResponse("Response result is null");
            }

            if (!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(response.getResponseResult().getReturnCode())) {
                logger.warn("MapRouteUtil.callQueryEstimateRoute", "response code is not success: {}",
                    response.getResponseResult().getReturnCode());
                return createErrorResponse("Service call failed with code: " + response.getResponseResult().getReturnCode());
            }

            Integer duration = response.getDuration();
            Integer distance = response.getDistance();

            if (duration == null || distance == null) {
                logger.warn("MapRouteUtil.callQueryEstimateRoute", "duration or distance is null");
                return createErrorResponse("Duration or distance is null");
            }

            // 构建成功响应
            RouteResponse routeResponse = new RouteResponse();
            routeResponse.setFromLongitude(request.getFromPoint().getLongitude());
            routeResponse.setFromLatitude(request.getFromPoint().getLatitude());
            routeResponse.setFromCoordsys(request.getFromPoint().getCoordsys());
            routeResponse.setToLongitude(request.getToPoint().getLongitude());
            routeResponse.setToLatitude(request.getToPoint().getLatitude());
            routeResponse.setToCoordsys(request.getToPoint().getCoordsys());
            routeResponse.setDistance(distance.doubleValue() / 1000); // 转换为公里
            routeResponse.setDuration(duration.doubleValue() / 60);   // 转换为分钟
            routeResponse.setSuccess(true);

            return routeResponse;

        } catch (Exception e) {
            logger.error("MapRouteUtil.callQueryEstimateRoute error", e);
            return createErrorResponse("Query estimate route failed: " + e.getMessage());
        }
    }

    /**
     * 构建QueryPredictRouteRequestType请求对象
     */
    private QueryPredictRouteRequestType buildQueryPredictRouteRequest(RouteRequest request) {
        QueryPredictRouteRequestType routeRequestType = new QueryPredictRouteRequestType();

        // 设置起点
        BaseGpsDTO from = new BaseGpsDTO();
        from.setLatitude(BigDecimal.valueOf(request.getFromPoint().getLatitude()));
        from.setLongitude(BigDecimal.valueOf(request.getFromPoint().getLongitude()));
        from.setCoordType(request.getFromPoint().getCoordsys());
        from.setCityId(request.getFromPoint().getCityId().longValue());
        routeRequestType.setOrigin(from);

        // 设置终点
        BaseGpsDTO to = new BaseGpsDTO();
        to.setLatitude(BigDecimal.valueOf(request.getToPoint().getLatitude()));
        to.setLongitude(BigDecimal.valueOf(request.getToPoint().getLongitude()));
        to.setCoordType(request.getToPoint().getCoordsys());
        to.setCityId(request.getToPoint().getCityId().longValue());
        routeRequestType.setDestination(to);

        // 设置出发时间和扩展信息
        if (request.getDepartureTime() != null) {
            routeRequestType.setDepartureTime(request.getDepartureTime().getTime());
            ExtendInfoDTO extendInfoDTO = new ExtendInfoDTO();
            extendInfoDTO.setForceGaodeFuture(true);
            routeRequestType.setExtendInfoDTO(extendInfoDTO);
        }

        routeRequestType.setOrderId(request.getOrderId());
        return routeRequestType;
    }

    /**
     * 创建错误响应
     */
    private RouteResponse createErrorResponse(String errorMessage) {
        RouteResponse errorResponse = new RouteResponse();
        errorResponse.setSuccess(false);
        errorResponse.setErrorMessage(errorMessage);
        return errorResponse;
    }
}
