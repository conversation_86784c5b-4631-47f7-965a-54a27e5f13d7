package com.ctrip.dcs.dsp.delay.infrastructure.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GaoDeFutureMonitorDTO {
    private String cityId;
    private String orderId;
    private String departureTime;
    private String origin;
    private String destination;
    private String realTimeDistance;
    private String realTimeDuration;
    private String futureDistance;
    private String futureDuration;
    private String baidufutureDistance;
    private String baidufutureDuration;
    private String executeTime;
    private String actualDistance;
    private String actualDuration;
    private String realTimeDiffDuration;
    private String actualDiffDuration;
    private String scene;
}
