package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * DelayDsp项目中MapRouteUtil的具体实现
 * 实现了抽象的兜底方法，调用项目中现有的GeoGateway
 * 
 * <AUTHOR>
 */
@Component
public class DelayDspMapRouteUtil extends MapRouteUtil {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspMapRouteUtil.class);

    @Autowired
    private GeoGateway geoGateway;

    /**
     * 单个请求的兜底方法实现
     * 调用项目中现有的GeoGateway.queryRoutes方法
     */
    @Override
    protected RouteResponse fallbackSingleMethod(RouteRequest request) {
        try {
            logger.info("DelayDspMapRouteUtil.fallbackSingleMethod", "using fallback single method for orderId: {}", request.getOrderId());
            
            // 构建Position对象
            Position position = buildPosition(request);
            List<Position> positions = Arrays.asList(position);
            
            // 调用现有的批量查询方法
            Integer cityId = request.getFromPoint().getCityId();
            List<Route> routes = geoGateway.queryRoutes(cityId, positions);
            
            if (CollectionUtils.isNotEmpty(routes)) {
                Route route = routes.get(0);
                RouteResponse response = buildRouteResponse(request, route);
                
                logger.info("DelayDspMapRouteUtil.fallbackSingleMethod", "successfully got route from fallback method, distance: {}, duration: {}", 
                    route.getDistance(), route.getDuration());
                return response;
            } else {
                logger.warn("DelayDspMapRouteUtil.fallbackSingleMethod", "fallback method returned empty routes");
                return createErrorResponse("Fallback method returned empty routes");
            }
            
        } catch (Exception e) {
            logger.error("DelayDspMapRouteUtil.fallbackSingleMethod error", e);
            return createErrorResponse("Fallback single method failed: " + e.getMessage());
        }
    }

    /**
     * 批量请求的兜底方法实现
     * 调用项目中现有的GeoGateway.queryRoutes方法
     */
    @Override
    protected List<RouteResponse> fallbackBatchMethod(List<RouteRequest> requests) {
        List<RouteResponse> responses = new ArrayList<>();
        
        try {
            logger.info("DelayDspMapRouteUtil.fallbackBatchMethod", "using fallback batch method for {} requests", requests.size());
            
            if (CollectionUtils.isEmpty(requests)) {
                return responses;
            }
            
            // 按城市分组处理（假设同一批次的请求城市相同，如果不同需要分组处理）
            Integer cityId = requests.get(0).getFromPoint().getCityId();
            
            // 构建Position列表
            List<Position> positions = new ArrayList<>();
            for (RouteRequest request : requests) {
                Position position = buildPosition(request);
                positions.add(position);
            }
            
            // 调用现有的批量查询方法
            List<Route> routes = geoGateway.queryRoutes(cityId, positions);
            
            if (CollectionUtils.isNotEmpty(routes)) {
                // 将Route结果转换为RouteResponse
                for (int i = 0; i < requests.size(); i++) {
                    RouteRequest request = requests.get(i);
                    
                    // 根据hash匹配对应的Route
                    Position position = positions.get(i);
                    String positionHash = position.hash();
                    
                    Route matchedRoute = routes.stream()
                        .filter(route -> positionHash.equals(route.getHash()))
                        .findFirst()
                        .orElse(null);
                    
                    if (matchedRoute != null) {
                        RouteResponse response = buildRouteResponse(request, matchedRoute);
                        responses.add(response);
                    } else {
                        // 如果没有匹配到，使用索引匹配（兜底）
                        if (i < routes.size()) {
                            RouteResponse response = buildRouteResponse(request, routes.get(i));
                            responses.add(response);
                        } else {
                            responses.add(createErrorResponse("No matching route found"));
                        }
                    }
                }
                
                logger.info("DelayDspMapRouteUtil.fallbackBatchMethod", "successfully processed {} requests, got {} responses", 
                    requests.size(), responses.size());
            } else {
                logger.warn("DelayDspMapRouteUtil.fallbackBatchMethod", "fallback method returned empty routes");
                // 为每个请求创建错误响应
                for (RouteRequest request : requests) {
                    responses.add(createErrorResponse("Fallback method returned empty routes"));
                }
            }
            
        } catch (Exception e) {
            logger.error("DelayDspMapRouteUtil.fallbackBatchMethod error", e);
            // 为每个请求创建错误响应
            for (RouteRequest request : requests) {
                responses.add(createErrorResponse("Fallback batch method failed: " + e.getMessage()));
            }
        }
        
        return responses;
    }

    /**
     * 将RouteRequest转换为Position对象
     */
    private Position buildPosition(RouteRequest request) {
        Position position = new Position();
        position.setFromLongitude(request.getFromPoint().getLongitude());
        position.setFromLatitude(request.getFromPoint().getLatitude());
        position.setFromCoordsys(request.getFromPoint().getCoordsys());
        position.setFromHash(GeoHashUtil.buildGeoHash(request.getFromPoint().getLongitude(), request.getFromPoint().getLatitude()));
        
        position.setToLongitude(request.getToPoint().getLongitude());
        position.setToLatitude(request.getToPoint().getLatitude());
        position.setToCoordsys(request.getToPoint().getCoordsys());
        position.setToHash(GeoHashUtil.buildGeoHash(request.getToPoint().getLongitude(), request.getToPoint().getLatitude()));
        
        position.setDepartureTime(request.getDepartureTime());
        
        return position;
    }

    /**
     * 根据RouteRequest和Route构建RouteResponse
     */
    private RouteResponse buildRouteResponse(RouteRequest request, Route route) {
        RouteResponse response = new RouteResponse();
        response.setFromLongitude(request.getFromPoint().getLongitude());
        response.setFromLatitude(request.getFromPoint().getLatitude());
        response.setFromCoordsys(request.getFromPoint().getCoordsys());
        response.setToLongitude(request.getToPoint().getLongitude());
        response.setToLatitude(request.getToPoint().getLatitude());
        response.setToCoordsys(request.getToPoint().getCoordsys());
        response.setDistance(route.getDistance());
        response.setDuration(route.getDuration());
        response.setSuccess(true);
        return response;
    }

    /**
     * 创建错误响应
     */
    private RouteResponse createErrorResponse(String errorMessage) {
        RouteResponse errorResponse = new RouteResponse();
        errorResponse.setSuccess(false);
        errorResponse.setErrorMessage(errorMessage);
        return errorResponse;
    }
}
